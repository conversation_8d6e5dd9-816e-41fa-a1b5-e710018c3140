<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}DV跟踪工具{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    {% load static %}
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'tracker:dashboard' %}">
                <i class="fas fa-microchip me-2"></i>DV跟踪工具
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" 
                           href="{% url 'tracker:dashboard' %}">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'compile_tasks' %}active{% endif %}" 
                           href="{% url 'tracker:compile_tasks' %}">
                            <i class="fas fa-hammer me-1"></i>编译任务
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'simulation_tasks' %}active{% endif %}" 
                           href="{% url 'tracker:simulation_tasks' %}">
                            <i class="fas fa-play-circle me-1"></i>仿真任务
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'verification_tasks' %}active{% endif %}" 
                           href="{% url 'tracker:verification_tasks' %}">
                            <i class="fas fa-check-circle me-1"></i>验证任务
                        </a>
                    </li>
                </ul>
                
                <!-- 搜索框 -->
                <form class="d-flex" id="searchForm">
                    <div class="input-group">
                        <input class="form-control" type="search" placeholder="搜索任务..." id="searchInput">
                        <button class="btn btn-outline-light" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid mt-4">
        <!-- 消息提示 -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">&copy; 2024 DV跟踪工具. 版权所有.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- 搜索功能 -->
    <script>
        $(document).ready(function() {
            $('#searchForm').on('submit', function(e) {
                e.preventDefault();
                const query = $('#searchInput').val().trim();
                if (query) {
                    performSearch(query);
                }
            });
            
            function performSearch(query) {
                $.ajax({
                    url: '{% url "tracker:search_tasks" %}',
                    data: { 'q': query },
                    success: function(data) {
                        showSearchResults(data);
                    },
                    error: function() {
                        alert('搜索失败，请稍后重试');
                    }
                });
            }
            
            function showSearchResults(data) {
                // 这里可以实现搜索结果的显示逻辑
                // 可以是模态框、下拉菜单或跳转到搜索结果页面
                console.log('搜索结果:', data);
                
                if (data.results.length > 0) {
                    let resultsHtml = '<div class="search-results"><h5>搜索结果:</h5><ul class="list-group">';
                    data.results.forEach(function(result) {
                        resultsHtml += `<li class="list-group-item">
                            <a href="${result.url}" class="text-decoration-none">
                                <strong>${result.name}</strong> - ${result.project} (${result.pc_node})
                                <span class="badge bg-secondary ms-2">${result.status}</span>
                            </a>
                        </li>`;
                    });
                    resultsHtml += '</ul></div>';
                    
                    // 显示搜索结果（这里简单用alert，实际应用中可以用模态框）
                    const modal = `
                        <div class="modal fade" id="searchModal" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">搜索结果</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">${resultsHtml}</div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    $('body').append(modal);
                    $('#searchModal').modal('show');
                    $('#searchModal').on('hidden.bs.modal', function() {
                        $(this).remove();
                    });
                } else {
                    alert('未找到相关结果');
                }
            }
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
