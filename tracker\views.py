from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse, Http404
from django.db.models import Q, Count
from django.core.paginator import Paginator
from django.contrib import messages
from django.views.decorators.http import require_http_methods
from django.utils import timezone
import os
import mimetypes
from .models import CompileTask, SimulationTask, VerificationTask, Project, PCNode, TaskStatus


def dashboard(request):
    """主页仪表板视图"""
    # 获取统计数据
    compile_stats = CompileTask.objects.aggregate(
        total=Count('id'),
        success=Count('id', filter=Q(status=TaskStatus.SUCCESS)),
        failed=Count('id', filter=Q(status=TaskStatus.FAILED)),
        running=Count('id', filter=Q(status=TaskStatus.RUNNING)),
        pending=Count('id', filter=Q(status=TaskStatus.PENDING))
    )
    
    simulation_stats = SimulationTask.objects.aggregate(
        total=Count('id'),
        success=Count('id', filter=Q(status=TaskStatus.SUCCESS)),
        failed=Count('id', filter=Q(status=TaskStatus.FAILED)),
        running=Count('id', filter=Q(status=TaskStatus.RUNNING)),
        pending=Count('id', filter=Q(status=TaskStatus.PENDING))
    )
    
    verification_stats = VerificationTask.objects.aggregate(
        total=Count('id'),
        success=Count('id', filter=Q(status=TaskStatus.SUCCESS)),
        failed=Count('id', filter=Q(status=TaskStatus.FAILED)),
        running=Count('id', filter=Q(status=TaskStatus.RUNNING)),
        pending=Count('id', filter=Q(status=TaskStatus.PENDING))
    )
    
    # 获取最近的任务
    recent_compile_tasks = CompileTask.objects.select_related('project', 'pc_node').order_by('-created_at')[:5]
    recent_simulation_tasks = SimulationTask.objects.select_related('project', 'pc_node').order_by('-created_at')[:5]
    recent_verification_tasks = VerificationTask.objects.select_related('project', 'pc_node').order_by('-created_at')[:5]
    
    context = {
        'compile_stats': compile_stats,
        'simulation_stats': simulation_stats,
        'verification_stats': verification_stats,
        'recent_compile_tasks': recent_compile_tasks,
        'recent_simulation_tasks': recent_simulation_tasks,
        'recent_verification_tasks': recent_verification_tasks,
    }
    
    return render(request, 'tracker/dashboard.html', context)


def compile_tasks(request):
    """编译任务列表视图"""
    tasks = CompileTask.objects.select_related('project', 'pc_node').all()
    
    # 搜索功能
    search_query = request.GET.get('search', '')
    if search_query:
        tasks = tasks.filter(
            Q(task_name__icontains=search_query) |
            Q(project__name__icontains=search_query) |
            Q(pc_node__pc_id__icontains=search_query)
        )
    
    # 状态过滤
    status_filter = request.GET.get('status', '')
    if status_filter:
        tasks = tasks.filter(status=status_filter)
    
    # 项目过滤
    project_filter = request.GET.get('project', '')
    if project_filter:
        tasks = tasks.filter(project_id=project_filter)
    
    # 排序
    sort_by = request.GET.get('sort', '-created_at')
    if sort_by in ['task_name', '-task_name', 'status', '-status', 'created_at', '-created_at', 'duration', '-duration']:
        tasks = tasks.order_by(sort_by)
    else:
        tasks = tasks.order_by('-created_at')
    
    # 分页
    paginator = Paginator(tasks, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # 获取过滤选项
    projects = Project.objects.all()
    status_choices = TaskStatus.choices
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'project_filter': project_filter,
        'sort_by': sort_by,
        'projects': projects,
        'status_choices': status_choices,
        'task_type': 'compile',
    }
    
    return render(request, 'tracker/task_list.html', context)


def simulation_tasks(request):
    """仿真任务列表视图"""
    tasks = SimulationTask.objects.select_related('project', 'pc_node', 'compile_task').all()
    
    # 搜索功能
    search_query = request.GET.get('search', '')
    if search_query:
        tasks = tasks.filter(
            Q(task_name__icontains=search_query) |
            Q(project__name__icontains=search_query) |
            Q(pc_node__pc_id__icontains=search_query)
        )
    
    # 状态过滤
    status_filter = request.GET.get('status', '')
    if status_filter:
        tasks = tasks.filter(status=status_filter)
    
    # 项目过滤
    project_filter = request.GET.get('project', '')
    if project_filter:
        tasks = tasks.filter(project_id=project_filter)
    
    # 排序
    sort_by = request.GET.get('sort', '-created_at')
    if sort_by in ['task_name', '-task_name', 'status', '-status', 'created_at', '-created_at', 'duration', '-duration']:
        tasks = tasks.order_by(sort_by)
    else:
        tasks = tasks.order_by('-created_at')
    
    # 分页
    paginator = Paginator(tasks, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # 获取过滤选项
    projects = Project.objects.all()
    status_choices = TaskStatus.choices
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'project_filter': project_filter,
        'sort_by': sort_by,
        'projects': projects,
        'status_choices': status_choices,
        'task_type': 'simulation',
    }
    
    return render(request, 'tracker/task_list.html', context)


def task_detail(request, task_type, task_id):
    """任务详情视图"""
    if task_type == 'compile':
        task = get_object_or_404(CompileTask, id=task_id)
        template = 'tracker/compile_detail.html'
    elif task_type == 'simulation':
        task = get_object_or_404(SimulationTask, id=task_id)
        template = 'tracker/simulation_detail.html'
    elif task_type == 'verification':
        task = get_object_or_404(VerificationTask, id=task_id)
        template = 'tracker/verification_detail.html'
    else:
        raise Http404("任务类型不存在")

    context = {
        'task': task,
        'task_type': task_type,
    }

    return render(request, template, context)


def view_log(request, task_type, task_id):
    """查看日志文件"""
    if task_type == 'compile':
        task = get_object_or_404(CompileTask, id=task_id)
    elif task_type == 'simulation':
        task = get_object_or_404(SimulationTask, id=task_id)
    elif task_type == 'verification':
        task = get_object_or_404(VerificationTask, id=task_id)
    else:
        raise Http404("任务类型不存在")

    if not task.log_path or not os.path.exists(task.log_path):
        messages.error(request, "日志文件不存在或路径无效")
        return render(request, 'tracker/log_view.html', {
            'task': task,
            'task_type': task_type,
            'error': '日志文件不存在或路径无效'
        })

    try:
        with open(task.log_path, 'r', encoding='utf-8', errors='ignore') as f:
            log_content = f.read()
    except Exception as e:
        messages.error(request, f"读取日志文件失败: {str(e)}")
        return render(request, 'tracker/log_view.html', {
            'task': task,
            'task_type': task_type,
            'error': f'读取日志文件失败: {str(e)}'
        })

    context = {
        'task': task,
        'task_type': task_type,
        'log_content': log_content,
    }

    return render(request, 'tracker/log_view.html', context)


def view_document(request, task_id):
    """查看验证任务文档"""
    task = get_object_or_404(VerificationTask, id=task_id)

    if not task.document_path or not os.path.exists(task.document_path):
        messages.error(request, "文档文件不存在或路径无效")
        return render(request, 'tracker/document_view.html', {
            'task': task,
            'error': '文档文件不存在或路径无效'
        })

    try:
        # 检测文件类型
        mime_type, _ = mimetypes.guess_type(task.document_path)

        if mime_type and mime_type.startswith('text/'):
            # 文本文件直接读取
            with open(task.document_path, 'r', encoding='utf-8', errors='ignore') as f:
                document_content = f.read()

            context = {
                'task': task,
                'document_content': document_content,
                'file_type': 'text',
            }
        else:
            # 其他文件类型提供下载链接
            context = {
                'task': task,
                'file_type': 'binary',
                'file_name': os.path.basename(task.document_path),
                'mime_type': mime_type,
            }
    except Exception as e:
        messages.error(request, f"读取文档文件失败: {str(e)}")
        return render(request, 'tracker/document_view.html', {
            'task': task,
            'error': f'读取文档文件失败: {str(e)}'
        })

    return render(request, 'tracker/document_view.html', context)


def download_document(request, task_id):
    """下载验证任务文档"""
    task = get_object_or_404(VerificationTask, id=task_id)

    if not task.document_path or not os.path.exists(task.document_path):
        raise Http404("文档文件不存在")

    try:
        with open(task.document_path, 'rb') as f:
            response = HttpResponse(f.read())

        mime_type, _ = mimetypes.guess_type(task.document_path)
        if mime_type:
            response['Content-Type'] = mime_type
        else:
            response['Content-Type'] = 'application/octet-stream'

        filename = os.path.basename(task.document_path)
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response
    except Exception as e:
        raise Http404(f"下载文件失败: {str(e)}")


@require_http_methods(["GET"])
def search_tasks(request):
    """全局搜索任务"""
    query = request.GET.get('q', '').strip()
    task_type = request.GET.get('type', 'all')

    if not query:
        return JsonResponse({'error': '搜索关键词不能为空'}, status=400)

    results = []

    if task_type in ['all', 'compile']:
        compile_tasks = CompileTask.objects.filter(
            Q(task_name__icontains=query) |
            Q(project__name__icontains=query) |
            Q(pc_node__pc_id__icontains=query)
        ).select_related('project', 'pc_node')[:10]

        for task in compile_tasks:
            results.append({
                'type': 'compile',
                'id': task.id,
                'name': task.task_name,
                'project': task.project.name,
                'pc_node': task.pc_node.pc_id,
                'status': task.get_status_display(),
                'created_at': task.created_at.strftime('%Y-%m-%d %H:%M'),
                'url': f'/tasks/compile/{task.id}/'
            })

    if task_type in ['all', 'simulation']:
        simulation_tasks = SimulationTask.objects.filter(
            Q(task_name__icontains=query) |
            Q(project__name__icontains=query) |
            Q(pc_node__pc_id__icontains=query)
        ).select_related('project', 'pc_node')[:10]

        for task in simulation_tasks:
            results.append({
                'type': 'simulation',
                'id': task.id,
                'name': task.task_name,
                'project': task.project.name,
                'pc_node': task.pc_node.pc_id,
                'status': task.get_status_display(),
                'created_at': task.created_at.strftime('%Y-%m-%d %H:%M'),
                'url': f'/tasks/simulation/{task.id}/'
            })

    if task_type in ['all', 'verification']:
        verification_tasks = VerificationTask.objects.filter(
            Q(task_name__icontains=query) |
            Q(project__name__icontains=query) |
            Q(pc_node__pc_id__icontains=query)
        ).select_related('project', 'pc_node')[:10]

        for task in verification_tasks:
            results.append({
                'type': 'verification',
                'id': task.id,
                'name': task.task_name,
                'project': task.project.name,
                'pc_node': task.pc_node.pc_id,
                'status': task.get_status_display(),
                'created_at': task.created_at.strftime('%Y-%m-%d %H:%M'),
                'url': f'/tasks/verification/{task.id}/'
            })

    return JsonResponse({
        'results': results,
        'total': len(results),
        'query': query
    })


def verification_tasks(request):
    """验证任务列表视图"""
    tasks = VerificationTask.objects.select_related('project', 'pc_node', 'simulation_task').all()
    
    # 搜索功能
    search_query = request.GET.get('search', '')
    if search_query:
        tasks = tasks.filter(
            Q(task_name__icontains=search_query) |
            Q(project__name__icontains=search_query) |
            Q(pc_node__pc_id__icontains=search_query)
        )
    
    # 状态过滤
    status_filter = request.GET.get('status', '')
    if status_filter:
        tasks = tasks.filter(status=status_filter)
    
    # 项目过滤
    project_filter = request.GET.get('project', '')
    if project_filter:
        tasks = tasks.filter(project_id=project_filter)
    
    # 排序
    sort_by = request.GET.get('sort', '-created_at')
    if sort_by in ['task_name', '-task_name', 'status', '-status', 'created_at', '-created_at', 'duration', '-duration']:
        tasks = tasks.order_by(sort_by)
    else:
        tasks = tasks.order_by('-created_at')
    
    # 分页
    paginator = Paginator(tasks, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # 获取过滤选项
    projects = Project.objects.all()
    status_choices = TaskStatus.choices
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'project_filter': project_filter,
        'sort_by': sort_by,
        'projects': projects,
        'status_choices': status_choices,
        'task_type': 'verification',
    }
    
    return render(request, 'tracker/task_list.html', context)
