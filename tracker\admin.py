from django.contrib import admin
from .models import Project, PCNode, CompileTask, SimulationTask, VerificationTask


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'created_at', 'updated_at']
    list_filter = ['created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(PCNode)
class PCNodeAdmin(admin.ModelAdmin):
    list_display = ['pc_id', 'hostname', 'ip_address', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['pc_id', 'hostname', 'ip_address']
    readonly_fields = ['created_at']


@admin.register(CompileTask)
class CompileTaskAdmin(admin.ModelAdmin):
    list_display = ['task_name', 'project', 'pc_node', 'status', 'duration_display', 'created_at']
    list_filter = ['status', 'project', 'pc_node', 'created_at']
    search_fields = ['task_name', 'project__name', 'pc_node__pc_id']
    readonly_fields = ['created_at', 'updated_at', 'duration']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('task_name', 'project', 'pc_node', 'status')
        }),
        ('执行信息', {
            'fields': ('command', 'log_path', 'start_time', 'end_time', 'duration')
        }),
        ('错误信息', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
        ('时间戳', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SimulationTask)
class SimulationTaskAdmin(admin.ModelAdmin):
    list_display = ['task_name', 'project', 'pc_node', 'status', 'duration_display', 'created_at']
    list_filter = ['status', 'project', 'pc_node', 'created_at']
    search_fields = ['task_name', 'project__name', 'pc_node__pc_id']
    readonly_fields = ['created_at', 'updated_at', 'duration']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('task_name', 'project', 'pc_node', 'compile_task', 'status')
        }),
        ('执行信息', {
            'fields': ('command', 'log_path', 'start_time', 'end_time', 'duration')
        }),
        ('警告和错误', {
            'fields': ('uvm_warnings', 'error_message'),
            'classes': ('collapse',)
        }),
        ('时间戳', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(VerificationTask)
class VerificationTaskAdmin(admin.ModelAdmin):
    list_display = ['task_name', 'project', 'pc_node', 'status', 'duration_display', 'created_at']
    list_filter = ['status', 'project', 'pc_node', 'created_at']
    search_fields = ['task_name', 'project__name', 'pc_node__pc_id']
    readonly_fields = ['created_at', 'updated_at', 'duration']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('task_name', 'project', 'pc_node', 'simulation_task', 'status')
        }),
        ('执行信息', {
            'fields': ('command', 'log_path', 'document_path', 'start_time', 'end_time', 'duration')
        }),
        ('错误和备注', {
            'fields': ('error_message', 'notes'),
            'classes': ('collapse',)
        }),
        ('时间戳', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
