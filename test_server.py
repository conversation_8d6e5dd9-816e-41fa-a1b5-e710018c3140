#!/usr/bin/env python
import os
import sys
import django
from django.core.management import execute_from_command_line

if __name__ == '__main__':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dv_tracker.settings')
    
    try:
        django.setup()
        print("Django setup successful!")
        print("Starting development server...")
        execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8080'])
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
