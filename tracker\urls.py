from django.urls import path
from . import views

app_name = 'tracker'

urlpatterns = [
    # 主页和仪表板
    path('', views.dashboard, name='dashboard'),
    
    # 任务列表页面
    path('compile/', views.compile_tasks, name='compile_tasks'),
    path('simulation/', views.simulation_tasks, name='simulation_tasks'),
    path('verification/', views.verification_tasks, name='verification_tasks'),
    
    # 任务详情页面
    path('tasks/<str:task_type>/<int:task_id>/', views.task_detail, name='task_detail'),
    
    # 日志查看
    path('tasks/<str:task_type>/<int:task_id>/log/', views.view_log, name='view_log'),
    
    # 文档查看和下载（仅验证任务）
    path('tasks/verification/<int:task_id>/document/', views.view_document, name='view_document'),
    path('tasks/verification/<int:task_id>/download/', views.download_document, name='download_document'),
    
    # 搜索API
    path('api/search/', views.search_tasks, name='search_tasks'),
]
