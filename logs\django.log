Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 17108
"GET /static/css/style.css HTTP/1.1" 200 6154
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 4127
Internal Server Error: /compile/
Traceback (most recent call last):
  File "D:\Python\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Python\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\DV_aug\tracker\views.py", line 107, in compile_tasks
    return render(request, 'tracker/task_list.html', context)
  File "D:\Python\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\Python\lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "D:\Python\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "D:\Python\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "D:\Python\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "D:\Python\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "D:\Python\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "D:\Python\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "D:\Python\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "D:\Python\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "D:\Python\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "D:\Python\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "D:\Python\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "D:\Python\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "D:\Python\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "D:\Python\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "D:\Python\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "D:\Python\lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "D:\Python\lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "D:\Python\lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'compile_tasks' not found. 'compile_tasks' is not a valid view function or pattern name.
"GET /compile/ HTTP/1.1" 500 175664
Internal Server Error: /compile/
Traceback (most recent call last):
  File "D:\Python\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Python\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\DV_aug\tracker\views.py", line 107, in compile_tasks
    return render(request, 'tracker/task_list.html', context)
  File "D:\Python\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\Python\lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "D:\Python\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "D:\Python\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "D:\Python\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "D:\Python\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "D:\Python\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "D:\Python\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "D:\Python\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "D:\Python\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "D:\Python\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "D:\Python\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "D:\Python\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "D:\Python\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "D:\Python\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "D:\Python\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "D:\Python\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "D:\Python\lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "D:\Python\lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "D:\Python\lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'compile_tasks' not found. 'compile_tasks' is not a valid view function or pattern name.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 17108
"GET /simulation/ HTTP/1.1" 200 14187
"GET /compile/ HTTP/1.1" 200 14156
"GET / HTTP/1.1" 200 17108
"GET /compile/ HTTP/1.1" 200 14156
"GET /simulation/ HTTP/1.1" 200 14187
"GET /verification/ HTTP/1.1" 200 12780
"GET / HTTP/1.1" 200 17108
"GET /compile/ HTTP/1.1" 200 14156
"GET / HTTP/1.1" 200 17108
"GET /tasks/verification/11/ HTTP/1.1" 200 16647
"GET /tasks/verification/11/log/ HTTP/1.1" 200 14785
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /tasks/verification/11/ HTTP/1.1" 200 16647
"GET /verification/ HTTP/1.1" 200 12780
"GET / HTTP/1.1" 200 17108
"GET /compile/ HTTP/1.1" 200 14156
"GET / HTTP/1.1" 200 17108
"GET / HTTP/1.1" 200 17108
"GET / HTTP/1.1" 200 17108
"GET /?id=7478c5c1-68bf-4ae9-a6c3-bd227d571d7d&vscodeBrowserReqId=1756110249348 HTTP/1.1" 200 17108
Not Found: /test/
"GET /test/ HTTP/1.1" 404 4109
"GET / HTTP/1.1" 200 17108
"GET /static/css/style.css HTTP/1.1" 304 0
Not Found: /test/
"GET /test/ HTTP/1.1" 404 4109
