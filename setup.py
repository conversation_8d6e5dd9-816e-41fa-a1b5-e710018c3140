#!/usr/bin/env python
"""
DV跟踪工具安装脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n{'='*50}")
    print(f"正在执行: {description}")
    print(f"命令: {command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"错误信息: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version}")
    return True

def check_mysql():
    """检查MySQL连接"""
    try:
        import pymysql
        print("✅ PyMySQL已安装")
        return True
    except ImportError:
        print("⚠️  PyMySQL未安装，将在依赖安装时处理")
        return True

def create_directories():
    """创建必要的目录"""
    directories = ['logs', 'media', 'staticfiles']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def setup_environment():
    """设置环境"""
    print("\n🚀 开始设置DV跟踪工具环境...")
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 创建必要目录
    create_directories()
    
    # 检查.env文件
    if not Path('.env').exists():
        if Path('.env.example').exists():
            print("📝 复制.env.example到.env")
            subprocess.run('cp .env.example .env', shell=True)
            print("⚠️  请编辑.env文件配置数据库连接信息")
        else:
            print("⚠️  未找到.env.example文件")
    
    # 安装Python依赖
    if not run_command('pip install -r requirements.txt', '安装Python依赖'):
        return False
    
    # 检查MySQL
    check_mysql()
    
    print("\n✅ 环境设置完成!")
    print("\n📋 下一步操作:")
    print("1. 编辑.env文件配置数据库连接")
    print("2. 创建MySQL数据库: CREATE DATABASE dv_tracker;")
    print("3. 运行: python setup.py migrate")
    print("4. 运行: python setup.py createuser")
    print("5. 运行: python setup.py runserver")
    
    return True

def migrate_database():
    """执行数据库迁移"""
    print("\n🗄️  开始数据库迁移...")
    
    if not run_command('python manage.py makemigrations', '生成迁移文件'):
        return False
    
    if not run_command('python manage.py migrate', '执行数据库迁移'):
        return False
    
    print("✅ 数据库迁移完成!")
    return True

def create_superuser():
    """创建超级用户"""
    print("\n👤 创建超级用户...")
    
    try:
        subprocess.run('python manage.py createsuperuser', shell=True, check=True)
        print("✅ 超级用户创建完成!")
        return True
    except subprocess.CalledProcessError:
        print("❌ 超级用户创建失败")
        return False

def create_sample_data():
    """创建示例数据"""
    print("\n📊 创建示例数据...")
    
    if not run_command('python manage.py create_sample_data --projects 3 --pc-nodes 5 --tasks 20', '创建示例数据'):
        return False
    
    print("✅ 示例数据创建完成!")
    return True

def run_server():
    """启动开发服务器"""
    print("\n🌐 启动开发服务器...")
    print("服务器将在 http://localhost:8000 启动")
    print("按 Ctrl+C 停止服务器")
    
    try:
        subprocess.run('python manage.py runserver', shell=True, check=True)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

def run_tests():
    """运行测试"""
    print("\n🧪 运行测试...")
    
    if not run_command('python manage.py test tracker', '运行测试'):
        return False
    
    print("✅ 测试完成!")
    return True

def collect_static():
    """收集静态文件"""
    print("\n📁 收集静态文件...")
    
    if not run_command('python manage.py collectstatic --noinput', '收集静态文件'):
        return False
    
    print("✅ 静态文件收集完成!")
    return True

def show_help():
    """显示帮助信息"""
    print("""
DV跟踪工具安装脚本

用法: python setup.py [命令]

可用命令:
  setup        - 设置开发环境
  migrate      - 执行数据库迁移
  createuser   - 创建超级用户
  sampledata   - 创建示例数据
  runserver    - 启动开发服务器
  test         - 运行测试
  collectstatic- 收集静态文件
  help         - 显示此帮助信息

示例:
  python setup.py setup      # 首次安装
  python setup.py migrate    # 迁移数据库
  python setup.py runserver  # 启动服务器
    """)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    
    if command == 'setup':
        setup_environment()
    elif command == 'migrate':
        migrate_database()
    elif command == 'createuser':
        create_superuser()
    elif command == 'sampledata':
        create_sample_data()
    elif command == 'runserver':
        run_server()
    elif command == 'test':
        run_tests()
    elif command == 'collectstatic':
        collect_static()
    elif command == 'help':
        show_help()
    else:
        print(f"❌ 未知命令: {command}")
        show_help()

if __name__ == '__main__':
    main()
