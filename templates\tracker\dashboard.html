{% extends 'base.html' %}
{% load static %}

{% block title %}仪表板 - DV跟踪工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>DV跟踪仪表板
        </h1>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <!-- 编译任务统计 -->
    <div class="col-md-4 mb-3">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-hammer me-2"></i>编译任务
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="text-primary">
                            <h4>{{ compile_stats.total }}</h4>
                            <small>总计</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-success">
                            <h4>{{ compile_stats.success }}</h4>
                            <small>成功</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-danger">
                            <h4>{{ compile_stats.failed }}</h4>
                            <small>失败</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-warning">
                            <h4>{{ compile_stats.running }}</h4>
                            <small>运行中</small>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'tracker:compile_tasks' %}" class="btn btn-primary btn-sm">
                        查看详情 <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 仿真任务统计 -->
    <div class="col-md-4 mb-3">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-play-circle me-2"></i>仿真任务
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="text-info">
                            <h4>{{ simulation_stats.total }}</h4>
                            <small>总计</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-success">
                            <h4>{{ simulation_stats.success }}</h4>
                            <small>成功</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-danger">
                            <h4>{{ simulation_stats.failed }}</h4>
                            <small>失败</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-warning">
                            <h4>{{ simulation_stats.running }}</h4>
                            <small>运行中</small>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'tracker:simulation_tasks' %}" class="btn btn-info btn-sm">
                        查看详情 <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 验证任务统计 -->
    <div class="col-md-4 mb-3">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-check-circle me-2"></i>验证任务
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="text-success">
                            <h4>{{ verification_stats.total }}</h4>
                            <small>总计</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-success">
                            <h4>{{ verification_stats.success }}</h4>
                            <small>成功</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-danger">
                            <h4>{{ verification_stats.failed }}</h4>
                            <small>失败</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-warning">
                            <h4>{{ verification_stats.running }}</h4>
                            <small>运行中</small>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'tracker:verification_tasks' %}" class="btn btn-success btn-sm">
                        查看详情 <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近任务 -->
<div class="row">
    <!-- 最近编译任务 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-hammer me-2"></i>最近编译任务
                </h5>
            </div>
            <div class="card-body p-0">
                {% if recent_compile_tasks %}
                    <div class="list-group list-group-flush">
                        {% for task in recent_compile_tasks %}
                            <a href="{% url 'tracker:task_detail' 'compile' task.id %}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ task.task_name|truncatechars:30 }}</h6>
                                    <small>{{ task.created_at|date:"m-d H:i" }}</small>
                                </div>
                                <p class="mb-1">{{ task.project.name }} - {{ task.pc_node.pc_id }}</p>
                                <small>
                                    <span class="badge bg-{% if task.status == 'success' %}success{% elif task.status == 'failed' %}danger{% elif task.status == 'running' %}warning{% else %}secondary{% endif %}">
                                        {{ task.get_status_display }}
                                    </span>
                                </small>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-3 text-muted">
                        暂无编译任务
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 最近仿真任务 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-play-circle me-2"></i>最近仿真任务
                </h5>
            </div>
            <div class="card-body p-0">
                {% if recent_simulation_tasks %}
                    <div class="list-group list-group-flush">
                        {% for task in recent_simulation_tasks %}
                            <a href="{% url 'tracker:task_detail' 'simulation' task.id %}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ task.task_name|truncatechars:30 }}</h6>
                                    <small>{{ task.created_at|date:"m-d H:i" }}</small>
                                </div>
                                <p class="mb-1">{{ task.project.name }} - {{ task.pc_node.pc_id }}</p>
                                <small>
                                    <span class="badge bg-{% if task.status == 'success' %}success{% elif task.status == 'failed' %}danger{% elif task.status == 'running' %}warning{% else %}secondary{% endif %}">
                                        {{ task.get_status_display }}
                                    </span>
                                </small>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-3 text-muted">
                        暂无仿真任务
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 最近验证任务 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-check-circle me-2"></i>最近验证任务
                </h5>
            </div>
            <div class="card-body p-0">
                {% if recent_verification_tasks %}
                    <div class="list-group list-group-flush">
                        {% for task in recent_verification_tasks %}
                            <a href="{% url 'tracker:task_detail' 'verification' task.id %}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ task.task_name|truncatechars:30 }}</h6>
                                    <small>{{ task.created_at|date:"m-d H:i" }}</small>
                                </div>
                                <p class="mb-1">{{ task.project.name }} - {{ task.pc_node.pc_id }}</p>
                                <small>
                                    <span class="badge bg-{% if task.status == 'success' %}success{% elif task.status == 'failed' %}danger{% elif task.status == 'running' %}warning{% else %}secondary{% endif %}">
                                        {{ task.get_status_display }}
                                    </span>
                                </small>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-3 text-muted">
                        暂无验证任务
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
