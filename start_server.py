#!/usr/bin/env python
import os
import sys
import subprocess

def start_server():
    print("🚀 启动DV跟踪工具...")
    print("=" * 50)
    
    # 设置环境变量
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dv_tracker.settings')
    
    try:
        # 启动服务器
        print("正在启动Django开发服务器...")
        print("服务器地址: http://127.0.0.1:8000")
        print("按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        # 使用subprocess启动服务器
        result = subprocess.run([
            sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000'
        ], cwd=os.getcwd())
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == '__main__':
    start_server()
