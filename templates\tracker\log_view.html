{% extends 'base.html' %}
{% load static %}

{% block title %}日志查看 - {{ task.task_name }} - DV跟踪工具{% endblock %}

{% block extra_css %}
<style>
    .log-content {
        max-height: 600px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;
    }
    .log-toolbar {
        position: sticky;
        top: 0;
        background: white;
        z-index: 10;
        border-bottom: 1px solid #dee2e6;
        padding: 10px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'tracker:dashboard' %}">首页</a></li>
                <li class="breadcrumb-item">
                    {% if task_type == 'compile' %}
                        <a href="{% url 'tracker:compile_tasks' %}">编译任务</a>
                    {% elif task_type == 'simulation' %}
                        <a href="{% url 'tracker:simulation_tasks' %}">仿真任务</a>
                    {% elif task_type == 'verification' %}
                        <a href="{% url 'tracker:verification_tasks' %}">验证任务</a>
                    {% endif %}
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'tracker:task_detail' task_type task.id %}">{{ task.task_name }}</a>
                </li>
                <li class="breadcrumb-item active">日志查看</li>
            </ol>
        </nav>
        
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-alt me-2"></i>日志查看
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'tracker:task_detail' task_type task.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回任务详情
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 任务信息 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <strong>任务名称:</strong> {{ task.task_name }}
            </div>
            <div class="col-md-3">
                <strong>项目:</strong> {{ task.project.name }}
            </div>
            <div class="col-md-3">
                <strong>PC节点:</strong> {{ task.pc_node.pc_id }}
            </div>
            <div class="col-md-3">
                <strong>状态:</strong> 
                <span class="badge bg-{% if task.status == 'success' %}success{% elif task.status == 'failed' %}danger{% elif task.status == 'running' %}warning{% elif task.status == 'pending' %}secondary{% else %}info{% endif %}">
                    {{ task.get_status_display }}
                </span>
            </div>
        </div>
    </div>
</div>

<!-- 日志内容 -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-terminal me-2"></i>日志内容
            </h5>
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-secondary" onclick="copyLogContent()">
                    <i class="fas fa-copy me-1"></i>复制
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="downloadLog()">
                    <i class="fas fa-download me-1"></i>下载
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="toggleWrap()">
                    <i class="fas fa-text-width me-1"></i>换行
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        {% if error %}
            <div class="alert alert-danger m-3">
                <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
            </div>
        {% elif log_content %}
            <div class="log-toolbar px-3">
                <div class="row">
                    <div class="col-md-6">
                        <strong>日志路径:</strong> 
                        <code>{{ task.log_path }}</code>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            文件大小: {{ log_content|length|filesizeformat }}
                        </small>
                    </div>
                </div>
            </div>
            <div class="log-content bg-dark text-light p-3" id="logContent">{{ log_content }}</div>
        {% else %}
            <div class="alert alert-info m-3">
                <i class="fas fa-info-circle me-2"></i>日志文件为空或无法读取。
            </div>
        {% endif %}
    </div>
</div>

<!-- 搜索功能 -->
{% if log_content %}
<div class="card mt-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="在日志中搜索...">
                    <button class="btn btn-outline-secondary" type="button" onclick="searchInLog()">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <span id="searchResults" class="text-muted me-3"></span>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary" onclick="previousMatch()" disabled id="prevBtn">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="nextMatch()" disabled id="nextBtn">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
let searchMatches = [];
let currentMatchIndex = -1;
let originalContent = '';

$(document).ready(function() {
    originalContent = $('#logContent').html();
    
    // 搜索框回车事件
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) {
            searchInLog();
        }
    });
});

function copyLogContent() {
    const logContent = document.getElementById('logContent').innerText;
    navigator.clipboard.writeText(logContent).then(function() {
        alert('日志内容已复制到剪贴板');
    }, function() {
        alert('复制失败，请手动选择并复制');
    });
}

function downloadLog() {
    const logContent = document.getElementById('logContent').innerText;
    const blob = new Blob([logContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '{{ task.task_name }}_log.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function toggleWrap() {
    const logContent = document.getElementById('logContent');
    if (logContent.style.whiteSpace === 'nowrap') {
        logContent.style.whiteSpace = 'pre-wrap';
    } else {
        logContent.style.whiteSpace = 'nowrap';
    }
}

function searchInLog() {
    const searchTerm = document.getElementById('searchInput').value.trim();
    if (!searchTerm) {
        clearSearch();
        return;
    }
    
    const logContent = document.getElementById('logContent');
    const content = originalContent;
    
    // 创建正则表达式进行搜索
    const regex = new RegExp(escapeRegExp(searchTerm), 'gi');
    const matches = content.match(regex);
    
    if (matches) {
        // 高亮显示搜索结果
        const highlightedContent = content.replace(regex, '<mark class="bg-warning text-dark">$&</mark>');
        logContent.innerHTML = highlightedContent;
        
        // 更新搜索结果信息
        searchMatches = document.querySelectorAll('#logContent mark');
        currentMatchIndex = 0;
        updateSearchResults();
        
        // 滚动到第一个匹配项
        if (searchMatches.length > 0) {
            searchMatches[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            searchMatches[0].classList.add('bg-danger', 'text-white');
        }
        
        // 启用导航按钮
        document.getElementById('prevBtn').disabled = false;
        document.getElementById('nextBtn').disabled = false;
    } else {
        document.getElementById('searchResults').textContent = '未找到匹配项';
        searchMatches = [];
        currentMatchIndex = -1;
        document.getElementById('prevBtn').disabled = true;
        document.getElementById('nextBtn').disabled = true;
    }
}

function clearSearch() {
    document.getElementById('logContent').innerHTML = originalContent;
    document.getElementById('searchInput').value = '';
    document.getElementById('searchResults').textContent = '';
    searchMatches = [];
    currentMatchIndex = -1;
    document.getElementById('prevBtn').disabled = true;
    document.getElementById('nextBtn').disabled = true;
}

function updateSearchResults() {
    if (searchMatches.length > 0) {
        document.getElementById('searchResults').textContent = 
            `${currentMatchIndex + 1} / ${searchMatches.length}`;
    }
}

function previousMatch() {
    if (searchMatches.length === 0) return;
    
    // 移除当前高亮
    searchMatches[currentMatchIndex].classList.remove('bg-danger', 'text-white');
    
    // 移动到上一个匹配项
    currentMatchIndex = (currentMatchIndex - 1 + searchMatches.length) % searchMatches.length;
    
    // 高亮当前匹配项并滚动到视图
    searchMatches[currentMatchIndex].classList.add('bg-danger', 'text-white');
    searchMatches[currentMatchIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    updateSearchResults();
}

function nextMatch() {
    if (searchMatches.length === 0) return;
    
    // 移除当前高亮
    searchMatches[currentMatchIndex].classList.remove('bg-danger', 'text-white');
    
    // 移动到下一个匹配项
    currentMatchIndex = (currentMatchIndex + 1) % searchMatches.length;
    
    // 高亮当前匹配项并滚动到视图
    searchMatches[currentMatchIndex].classList.add('bg-danger', 'text-white');
    searchMatches[currentMatchIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    updateSearchResults();
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
</script>
{% endblock %}
