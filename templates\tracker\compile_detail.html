{% extends 'base.html' %}
{% load static %}

{% block title %}编译任务详情 - {{ task.task_name }} - DV跟踪工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'tracker:dashboard' %}">首页</a></li>
                <li class="breadcrumb-item"><a href="{% url 'tracker:compile_tasks' %}">编译任务</a></li>
                <li class="breadcrumb-item active">{{ task.task_name }}</li>
            </ol>
        </nav>
        
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-hammer me-2"></i>编译任务详情
            </h1>
            <div class="btn-group" role="group">
                {% if task.log_path %}
                    <a href="{% url 'tracker:view_log' 'compile' task.id %}" class="btn btn-info">
                        <i class="fas fa-file-alt me-1"></i>查看日志
                    </a>
                {% endif %}
                <a href="{% url 'tracker:compile_tasks' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回列表
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 基本信息 -->
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">任务名称:</dt>
                            <dd class="col-sm-8">{{ task.task_name }}</dd>
                            
                            <dt class="col-sm-4">项目:</dt>
                            <dd class="col-sm-8">{{ task.project.name }}</dd>
                            
                            <dt class="col-sm-4">PC节点:</dt>
                            <dd class="col-sm-8">{{ task.pc_node.pc_id }} ({{ task.pc_node.hostname }})</dd>
                            
                            <dt class="col-sm-4">状态:</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-{% if task.status == 'success' %}success{% elif task.status == 'failed' %}danger{% elif task.status == 'running' %}warning{% elif task.status == 'pending' %}secondary{% else %}info{% endif %} fs-6">
                                    {{ task.get_status_display }}
                                </span>
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">开始时间:</dt>
                            <dd class="col-sm-8">
                                {% if task.start_time %}
                                    {{ task.start_time|date:"Y-m-d H:i:s" }}
                                {% else %}
                                    <span class="text-muted">未开始</span>
                                {% endif %}
                            </dd>
                            
                            <dt class="col-sm-4">结束时间:</dt>
                            <dd class="col-sm-8">
                                {% if task.end_time %}
                                    {{ task.end_time|date:"Y-m-d H:i:s" }}
                                {% else %}
                                    <span class="text-muted">未结束</span>
                                {% endif %}
                            </dd>
                            
                            <dt class="col-sm-4">执行时长:</dt>
                            <dd class="col-sm-8">{{ task.duration_display }}</dd>
                            
                            <dt class="col-sm-4">创建时间:</dt>
                            <dd class="col-sm-8">{{ task.created_at|date:"Y-m-d H:i:s" }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- 执行命令 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-terminal me-2"></i>执行命令
                </h5>
            </div>
            <div class="card-body">
                <pre class="bg-dark text-light p-3 rounded"><code>{{ task.command }}</code></pre>
            </div>
        </div>

        <!-- 错误信息 -->
        {% if task.error_message %}
            <div class="card mb-4">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>错误信息
                    </h5>
                </div>
                <div class="card-body">
                    <pre class="bg-light p-3 rounded text-danger"><code>{{ task.error_message }}</code></pre>
                </div>
            </div>
        {% endif %}

        <!-- 日志路径 -->
        {% if task.log_path %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>日志文件
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-2"><strong>日志路径:</strong></p>
                    <code class="bg-light p-2 rounded d-block">{{ task.log_path }}</code>
                    <div class="mt-3">
                        <a href="{% url 'tracker:view_log' 'compile' task.id %}" class="btn btn-info">
                            <i class="fas fa-eye me-1"></i>查看日志内容
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- 侧边栏 -->
    <div class="col-md-4">
        <!-- 快速操作 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if task.log_path %}
                        <a href="{% url 'tracker:view_log' 'compile' task.id %}" class="btn btn-info">
                            <i class="fas fa-file-alt me-1"></i>查看日志
                        </a>
                    {% endif %}
                    <a href="{% url 'tracker:compile_tasks' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-1"></i>返回任务列表
                    </a>
                </div>
            </div>
        </div>

        <!-- 相关任务 -->
        {% if task.simulationtask_set.exists %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link me-2"></i>相关仿真任务
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for sim_task in task.simulationtask_set.all %}
                            <a href="{% url 'tracker:task_detail' 'simulation' sim_task.id %}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ sim_task.task_name|truncatechars:25 }}</h6>
                                    <small>{{ sim_task.created_at|date:"m-d H:i" }}</small>
                                </div>
                                <small>
                                    <span class="badge bg-{% if sim_task.status == 'success' %}success{% elif sim_task.status == 'failed' %}danger{% elif sim_task.status == 'running' %}warning{% else %}secondary{% endif %}">
                                        {{ sim_task.get_status_display }}
                                    </span>
                                </small>
                            </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- 项目信息 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-project-diagram me-2"></i>项目信息
                </h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-5">项目名称:</dt>
                    <dd class="col-sm-7">{{ task.project.name }}</dd>
                    
                    {% if task.project.description %}
                        <dt class="col-sm-5">项目描述:</dt>
                        <dd class="col-sm-7">{{ task.project.description|truncatechars:100 }}</dd>
                    {% endif %}
                    
                    <dt class="col-sm-5">PC节点:</dt>
                    <dd class="col-sm-7">{{ task.pc_node.pc_id }}</dd>
                    
                    <dt class="col-sm-5">主机名:</dt>
                    <dd class="col-sm-7">{{ task.pc_node.hostname }}</dd>
                    
                    {% if task.pc_node.ip_address %}
                        <dt class="col-sm-5">IP地址:</dt>
                        <dd class="col-sm-7">{{ task.pc_node.ip_address }}</dd>
                    {% endif %}
                </dl>
            </div>
        </div>
    </div>
</div>
{% endblock %}
