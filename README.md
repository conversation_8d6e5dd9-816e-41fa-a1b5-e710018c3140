# DV跟踪工具 (Design Verification Tracker)

DV跟踪工具是一个基于Django的Web应用程序，用于监控和管理硬件设计验证环境中的编译任务、仿真运行和相关验证任务的状态。该系统为验证工程师和项目经理提供集中化的任务执行跟踪、状态监控和文档管理功能。

## 功能特性

### 🔧 编译任务管理
- 监控编译任务状态和进度
- 查看编译命令和日志
- 跟踪编译时间和错误信息
- 支持按项目、状态、PC节点过滤

### 🎮 仿真任务跟踪
- 跟踪仿真任务执行状态
- 记录UVM警告和错误信息
- 监控仿真执行时间
- 关联编译任务依赖关系

### ✅ 验证任务监控
- 管理验证任务流程
- 查看验证文档和报告
- 跟踪验证覆盖率和结果
- 支持文档在线查看和下载

### 📊 统一仪表板
- 实时任务状态统计
- 最近任务活动展示
- 项目进度概览
- 快速导航和搜索

### 🔍 强大搜索功能
- 全局任务搜索
- 多条件过滤
- 实时搜索结果
- 智能排序功能

## 技术栈

- **后端**: Django 4.2.7
- **数据库**: MySQL (支持PyMySQL)
- **前端**: Bootstrap 5 + jQuery
- **样式**: Font Awesome 图标
- **部署**: 支持Docker和传统部署

## 快速开始

### 环境要求

- Python 3.8+
- MySQL 5.7+ 或 8.0+
- pip 包管理器

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd DV_aug
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # 或
   venv\Scripts\activate     # Windows
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **配置数据库**
   - 复制 `.env.example` 为 `.env`
   - 修改数据库配置信息
   ```bash
   cp .env.example .env
   ```
   
   编辑 `.env` 文件:
   ```env
   SECRET_KEY=your-secret-key-here
   DEBUG=True
   DB_NAME=dv_tracker
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   DB_HOST=localhost
   DB_PORT=3306
   ```

5. **创建数据库**
   ```sql
   CREATE DATABASE dv_tracker CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

6. **执行数据库迁移**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

7. **创建超级用户**
   ```bash
   python manage.py createsuperuser
   ```

8. **创建示例数据（可选）**
   ```bash
   python manage.py create_sample_data --projects 3 --pc-nodes 5 --tasks 20
   ```

9. **启动开发服务器**
   ```bash
   python manage.py runserver
   ```

10. **访问应用**
    - 主页: http://localhost:8000/
    - 管理后台: http://localhost:8000/admin/

## 项目结构

```
DV_aug/
├── dv_tracker/              # Django项目配置
│   ├── __init__.py
│   ├── settings.py          # 项目设置
│   ├── urls.py             # 主URL配置
│   ├── wsgi.py             # WSGI配置
│   └── asgi.py             # ASGI配置
├── tracker/                 # 主应用
│   ├── models.py           # 数据模型
│   ├── views.py            # 视图函数
│   ├── urls.py             # URL路由
│   ├── admin.py            # 管理后台
│   ├── apps.py             # 应用配置
│   ├── tests.py            # 测试用例
│   ├── migrations/         # 数据库迁移
│   └── management/         # 管理命令
│       └── commands/
│           └── create_sample_data.py
├── templates/              # HTML模板
│   ├── base.html           # 基础模板
│   └── tracker/            # 应用模板
│       ├── dashboard.html
│       ├── task_list.html
│       ├── compile_detail.html
│       ├── simulation_detail.html
│       ├── verification_detail.html
│       ├── log_view.html
│       └── document_view.html
├── static/                 # 静态文件
│   └── css/
│       └── style.css       # 自定义样式
├── logs/                   # 日志目录
├── media/                  # 媒体文件
├── manage.py               # Django管理脚本
├── requirements.txt        # Python依赖
├── .env.example           # 环境变量示例
└── README.md              # 项目说明
```

## 使用指南

### 1. 仪表板
访问主页查看系统概览，包括：
- 各类任务统计信息
- 最近任务活动
- 快速导航链接

### 2. 任务管理
- **编译任务**: 查看编译状态、日志和错误信息
- **仿真任务**: 监控仿真进度、UVM警告
- **验证任务**: 管理验证流程、查看报告

### 3. 搜索和过滤
- 使用顶部搜索框进行全局搜索
- 在任务列表页面使用过滤器
- 支持按状态、项目、时间排序

### 4. 日志查看
- 点击任务详情页的"查看日志"按钮
- 支持日志内容搜索和高亮
- 提供日志下载功能

### 5. 文档管理
- 验证任务支持文档查看
- 在线预览文本文档
- 下载二进制文档文件

## 开发指南

### 添加新功能

1. **创建模型**
   ```python
   # tracker/models.py
   class NewModel(models.Model):
       # 定义字段
       pass
   ```

2. **创建迁移**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

3. **添加视图**
   ```python
   # tracker/views.py
   def new_view(request):
       # 视图逻辑
       pass
   ```

4. **配置URL**
   ```python
   # tracker/urls.py
   urlpatterns = [
       path('new-url/', views.new_view, name='new_view'),
   ]
   ```

### 运行测试

```bash
python manage.py test tracker
```

### 代码风格

项目遵循PEP 8代码规范，建议使用以下工具：
- `flake8` - 代码检查
- `black` - 代码格式化
- `isort` - 导入排序

## 部署

### 生产环境配置

1. **设置环境变量**
   ```env
   DEBUG=False
   SECRET_KEY=your-production-secret-key
   ALLOWED_HOSTS=your-domain.com
   ```

2. **收集静态文件**
   ```bash
   python manage.py collectstatic
   ```

3. **使用Gunicorn**
   ```bash
   pip install gunicorn
   gunicorn dv_tracker.wsgi:application
   ```

4. **配置Nginx**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location /static/ {
           alias /path/to/staticfiles/;
       }
       
       location / {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 支持

如果您遇到问题或有建议，请：
1. 查看 [Issues](../../issues) 页面
2. 创建新的 Issue
3. 联系项目维护者

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础任务管理功能
- 仪表板和搜索功能
- 日志和文档查看功能
