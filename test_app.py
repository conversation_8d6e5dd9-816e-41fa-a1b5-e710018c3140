#!/usr/bin/env python
import os
import sys
import django
from django.test import Client
from django.urls import reverse

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dv_tracker.settings')
django.setup()

def test_views():
    print("测试Django应用...")
    
    # 创建测试客户端
    client = Client()
    
    try:
        # 测试主页
        print("测试主页...")
        response = client.get('/')
        print(f"主页状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 主页访问成功!")
        else:
            print(f"❌ 主页访问失败: {response.status_code}")
            
        # 测试编译任务页面
        print("测试编译任务页面...")
        response = client.get('/compile/')
        print(f"编译任务页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 编译任务页面访问成功!")
        else:
            print(f"❌ 编译任务页面访问失败: {response.status_code}")
            
        # 测试仿真任务页面
        print("测试仿真任务页面...")
        response = client.get('/simulation/')
        print(f"仿真任务页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 仿真任务页面访问成功!")
        else:
            print(f"❌ 仿真任务页面访问失败: {response.status_code}")
            
        print("\n🎉 Django应用测试完成!")
        print("应用运行正常，可以启动服务器了。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_views()
