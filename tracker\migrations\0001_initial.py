# Generated by Django 4.2.7 on 2025-08-25 08:02

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CompileTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_name', models.CharField(max_length=200, verbose_name='任务名称')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '运行中'), ('success', '成功'), ('failed', '失败'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='状态')),
                ('command', models.TextField(verbose_name='执行命令')),
                ('log_path', models.CharField(blank=True, max_length=500, verbose_name='日志路径')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('duration', models.DurationField(blank=True, null=True, verbose_name='执行时长')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '编译任务',
                'verbose_name_plural': '编译任务',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PCNode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pc_id', models.CharField(max_length=50, unique=True, verbose_name='PC ID')),
                ('hostname', models.CharField(max_length=100, verbose_name='主机名')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': 'PC节点',
                'verbose_name_plural': 'PC节点',
                'ordering': ['pc_id'],
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='项目名称')),
                ('description', models.TextField(blank=True, verbose_name='项目描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '项目',
                'verbose_name_plural': '项目',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SimulationTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_name', models.CharField(max_length=200, verbose_name='仿真名称')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '运行中'), ('success', '成功'), ('failed', '失败'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='状态')),
                ('command', models.TextField(verbose_name='执行命令')),
                ('log_path', models.CharField(blank=True, max_length=500, verbose_name='日志路径')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('duration', models.DurationField(blank=True, null=True, verbose_name='执行时长')),
                ('uvm_warnings', models.TextField(blank=True, verbose_name='UVM警告')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('compile_task', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='tracker.compiletask', verbose_name='关联编译任务')),
                ('pc_node', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tracker.pcnode', verbose_name='PC节点')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tracker.project', verbose_name='项目')),
            ],
            options={
                'verbose_name': '仿真任务',
                'verbose_name_plural': '仿真任务',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='compiletask',
            name='pc_node',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tracker.pcnode', verbose_name='PC节点'),
        ),
        migrations.AddField(
            model_name='compiletask',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tracker.project', verbose_name='项目'),
        ),
        migrations.CreateModel(
            name='VerificationTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_name', models.CharField(max_length=200, verbose_name='验证任务名称')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '运行中'), ('success', '成功'), ('failed', '失败'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='状态')),
                ('command', models.TextField(verbose_name='执行命令')),
                ('log_path', models.CharField(blank=True, max_length=500, verbose_name='日志路径')),
                ('document_path', models.CharField(blank=True, max_length=500, verbose_name='文档路径')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('duration', models.DurationField(blank=True, null=True, verbose_name='执行时长')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('pc_node', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tracker.pcnode', verbose_name='PC节点')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tracker.project', verbose_name='项目')),
                ('simulation_task', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='tracker.simulationtask', verbose_name='关联仿真任务')),
            ],
            options={
                'verbose_name': '验证任务',
                'verbose_name_plural': '验证任务',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['status'], name='tracker_ver_status_29a712_idx'), models.Index(fields=['project', 'status'], name='tracker_ver_project_1d5c44_idx'), models.Index(fields=['created_at'], name='tracker_ver_created_d18ae8_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='simulationtask',
            index=models.Index(fields=['status'], name='tracker_sim_status_03ad7c_idx'),
        ),
        migrations.AddIndex(
            model_name='simulationtask',
            index=models.Index(fields=['project', 'status'], name='tracker_sim_project_9c97d5_idx'),
        ),
        migrations.AddIndex(
            model_name='simulationtask',
            index=models.Index(fields=['created_at'], name='tracker_sim_created_397751_idx'),
        ),
        migrations.AddIndex(
            model_name='compiletask',
            index=models.Index(fields=['status'], name='tracker_com_status_52ef56_idx'),
        ),
        migrations.AddIndex(
            model_name='compiletask',
            index=models.Index(fields=['project', 'status'], name='tracker_com_project_345e58_idx'),
        ),
        migrations.AddIndex(
            model_name='compiletask',
            index=models.Index(fields=['created_at'], name='tracker_com_created_d54e91_idx'),
        ),
    ]
