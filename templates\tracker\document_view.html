{% extends 'base.html' %}
{% load static %}

{% block title %}文档查看 - {{ task.task_name }} - DV跟踪工具{% endblock %}

{% block extra_css %}
<style>
    .document-content {
        max-height: 600px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;
    }
    .document-toolbar {
        position: sticky;
        top: 0;
        background: white;
        z-index: 10;
        border-bottom: 1px solid #dee2e6;
        padding: 10px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'tracker:dashboard' %}">首页</a></li>
                <li class="breadcrumb-item"><a href="{% url 'tracker:verification_tasks' %}">验证任务</a></li>
                <li class="breadcrumb-item">
                    <a href="{% url 'tracker:task_detail' 'verification' task.id %}">{{ task.task_name }}</a>
                </li>
                <li class="breadcrumb-item active">文档查看</li>
            </ol>
        </nav>
        
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-text me-2"></i>文档查看
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'tracker:download_document' task.id %}" class="btn btn-success">
                    <i class="fas fa-download me-1"></i>下载文档
                </a>
                <a href="{% url 'tracker:task_detail' 'verification' task.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回任务详情
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 任务信息 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <strong>任务名称:</strong> {{ task.task_name }}
            </div>
            <div class="col-md-3">
                <strong>项目:</strong> {{ task.project.name }}
            </div>
            <div class="col-md-3">
                <strong>PC节点:</strong> {{ task.pc_node.pc_id }}
            </div>
            <div class="col-md-3">
                <strong>状态:</strong> 
                <span class="badge bg-{% if task.status == 'success' %}success{% elif task.status == 'failed' %}danger{% elif task.status == 'running' %}warning{% elif task.status == 'pending' %}secondary{% else %}info{% endif %}">
                    {{ task.get_status_display }}
                </span>
            </div>
        </div>
    </div>
</div>

<!-- 文档内容 -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-file-text me-2"></i>文档内容
            </h5>
            {% if file_type == 'text' %}
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-secondary" onclick="copyDocumentContent()">
                        <i class="fas fa-copy me-1"></i>复制
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="toggleWrap()">
                        <i class="fas fa-text-width me-1"></i>换行
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
    <div class="card-body p-0">
        {% if error %}
            <div class="alert alert-danger m-3">
                <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
            </div>
        {% elif file_type == 'text' and document_content %}
            <div class="document-toolbar px-3">
                <div class="row">
                    <div class="col-md-8">
                        <strong>文档路径:</strong> 
                        <code>{{ task.document_path }}</code>
                    </div>
                    <div class="col-md-4 text-end">
                        <small class="text-muted">
                            文件大小: {{ document_content|length|filesizeformat }}
                        </small>
                    </div>
                </div>
            </div>
            <div class="document-content bg-light p-3" id="documentContent">{{ document_content }}</div>
        {% elif file_type == 'binary' %}
            <div class="alert alert-info m-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <i class="fas fa-file me-2"></i>
                        <strong>二进制文件:</strong> {{ file_name }}
                        {% if mime_type %}
                            <br><small class="text-muted">文件类型: {{ mime_type }}</small>
                        {% endif %}
                        <br><strong>文档路径:</strong> <code>{{ task.document_path }}</code>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{% url 'tracker:download_document' task.id %}" class="btn btn-success">
                            <i class="fas fa-download me-1"></i>下载文件
                        </a>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="alert alert-info m-3">
                <i class="fas fa-info-circle me-2"></i>文档文件为空或无法读取。
            </div>
        {% endif %}
    </div>
</div>

<!-- 搜索功能 -->
{% if file_type == 'text' and document_content %}
<div class="card mt-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="在文档中搜索...">
                    <button class="btn btn-outline-secondary" type="button" onclick="searchInDocument()">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <span id="searchResults" class="text-muted me-3"></span>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary" onclick="previousMatch()" disabled id="prevBtn">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="nextMatch()" disabled id="nextBtn">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
let searchMatches = [];
let currentMatchIndex = -1;
let originalContent = '';

$(document).ready(function() {
    {% if file_type == 'text' and document_content %}
    originalContent = $('#documentContent').html();
    
    // 搜索框回车事件
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) {
            searchInDocument();
        }
    });
    {% endif %}
});

function copyDocumentContent() {
    const documentContent = document.getElementById('documentContent').innerText;
    navigator.clipboard.writeText(documentContent).then(function() {
        alert('文档内容已复制到剪贴板');
    }, function() {
        alert('复制失败，请手动选择并复制');
    });
}

function toggleWrap() {
    const documentContent = document.getElementById('documentContent');
    if (documentContent.style.whiteSpace === 'nowrap') {
        documentContent.style.whiteSpace = 'pre-wrap';
    } else {
        documentContent.style.whiteSpace = 'nowrap';
    }
}

function searchInDocument() {
    const searchTerm = document.getElementById('searchInput').value.trim();
    if (!searchTerm) {
        clearSearch();
        return;
    }
    
    const documentContent = document.getElementById('documentContent');
    const content = originalContent;
    
    // 创建正则表达式进行搜索
    const regex = new RegExp(escapeRegExp(searchTerm), 'gi');
    const matches = content.match(regex);
    
    if (matches) {
        // 高亮显示搜索结果
        const highlightedContent = content.replace(regex, '<mark class="bg-warning text-dark">$&</mark>');
        documentContent.innerHTML = highlightedContent;
        
        // 更新搜索结果信息
        searchMatches = document.querySelectorAll('#documentContent mark');
        currentMatchIndex = 0;
        updateSearchResults();
        
        // 滚动到第一个匹配项
        if (searchMatches.length > 0) {
            searchMatches[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            searchMatches[0].classList.add('bg-danger', 'text-white');
        }
        
        // 启用导航按钮
        document.getElementById('prevBtn').disabled = false;
        document.getElementById('nextBtn').disabled = false;
    } else {
        document.getElementById('searchResults').textContent = '未找到匹配项';
        searchMatches = [];
        currentMatchIndex = -1;
        document.getElementById('prevBtn').disabled = true;
        document.getElementById('nextBtn').disabled = true;
    }
}

function clearSearch() {
    document.getElementById('documentContent').innerHTML = originalContent;
    document.getElementById('searchInput').value = '';
    document.getElementById('searchResults').textContent = '';
    searchMatches = [];
    currentMatchIndex = -1;
    document.getElementById('prevBtn').disabled = true;
    document.getElementById('nextBtn').disabled = true;
}

function updateSearchResults() {
    if (searchMatches.length > 0) {
        document.getElementById('searchResults').textContent = 
            `${currentMatchIndex + 1} / ${searchMatches.length}`;
    }
}

function previousMatch() {
    if (searchMatches.length === 0) return;
    
    // 移除当前高亮
    searchMatches[currentMatchIndex].classList.remove('bg-danger', 'text-white');
    
    // 移动到上一个匹配项
    currentMatchIndex = (currentMatchIndex - 1 + searchMatches.length) % searchMatches.length;
    
    // 高亮当前匹配项并滚动到视图
    searchMatches[currentMatchIndex].classList.add('bg-danger', 'text-white');
    searchMatches[currentMatchIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    updateSearchResults();
}

function nextMatch() {
    if (searchMatches.length === 0) return;
    
    // 移除当前高亮
    searchMatches[currentMatchIndex].classList.remove('bg-danger', 'text-white');
    
    // 移动到下一个匹配项
    currentMatchIndex = (currentMatchIndex + 1) % searchMatches.length;
    
    // 高亮当前匹配项并滚动到视图
    searchMatches[currentMatchIndex].classList.add('bg-danger', 'text-white');
    searchMatches[currentMatchIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    updateSearchResults();
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
</script>
{% endblock %}
