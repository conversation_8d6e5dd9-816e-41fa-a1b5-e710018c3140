from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
import random
from tracker.models import Project, PCNode, CompileTask, SimulationTask, VerificationTask, TaskStatus


class Command(BaseCommand):
    help = '创建示例数据用于测试DV跟踪工具'

    def add_arguments(self, parser):
        parser.add_argument(
            '--projects',
            type=int,
            default=3,
            help='要创建的项目数量'
        )
        parser.add_argument(
            '--pc-nodes',
            type=int,
            default=5,
            help='要创建的PC节点数量'
        )
        parser.add_argument(
            '--tasks',
            type=int,
            default=20,
            help='每种类型要创建的任务数量'
        )

    def handle(self, *args, **options):
        self.stdout.write('开始创建示例数据...')

        # 创建项目
        projects = []
        for i in range(options['projects']):
            project = Project.objects.create(
                name=f'项目_{i+1}',
                description=f'这是第{i+1}个测试项目，用于验证DV跟踪工具的功能。'
            )
            projects.append(project)
            self.stdout.write(f'创建项目: {project.name}')

        # 创建PC节点
        pc_nodes = []
        for i in range(options['pc_nodes']):
            pc_node = PCNode.objects.create(
                pc_id=f'PC{i+1:03d}',
                hostname=f'dv-pc-{i+1:02d}',
                ip_address=f'192.168.1.{100+i}',
                description=f'设计验证PC节点{i+1}'
            )
            pc_nodes.append(pc_node)
            self.stdout.write(f'创建PC节点: {pc_node.pc_id}')

        # 创建编译任务
        compile_tasks = []
        for i in range(options['tasks']):
            project = random.choice(projects)
            pc_node = random.choice(pc_nodes)
            
            # 随机生成时间
            created_time = timezone.now() - timedelta(days=random.randint(0, 30))
            start_time = created_time + timedelta(minutes=random.randint(1, 60))
            
            status = random.choice([s[0] for s in TaskStatus.choices])
            end_time = None
            error_message = ""
            
            if status in [TaskStatus.SUCCESS, TaskStatus.FAILED]:
                duration_minutes = random.randint(5, 120)
                end_time = start_time + timedelta(minutes=duration_minutes)
                
                if status == TaskStatus.FAILED:
                    error_message = f"编译错误: 在文件 module_{i}.v 第{random.randint(10, 100)}行发现语法错误"

            compile_task = CompileTask.objects.create(
                project=project,
                pc_node=pc_node,
                task_name=f'{project.name}_编译任务_{i+1}',
                command=f'make compile TARGET=module_{i} OPTS="-O2 -g"',
                status=status,
                log_path=f'/logs/compile/{project.name.lower()}/compile_{i+1}.log',
                start_time=start_time,
                end_time=end_time,
                error_message=error_message,
                created_at=created_time
            )
            compile_tasks.append(compile_task)

        self.stdout.write(f'创建了 {len(compile_tasks)} 个编译任务')

        # 创建仿真任务
        simulation_tasks = []
        for i in range(options['tasks']):
            project = random.choice(projects)
            pc_node = random.choice(pc_nodes)
            compile_task = random.choice([ct for ct in compile_tasks if ct.project == project]) if random.random() > 0.3 else None
            
            created_time = timezone.now() - timedelta(days=random.randint(0, 25))
            start_time = created_time + timedelta(minutes=random.randint(1, 60))
            
            status = random.choice([s[0] for s in TaskStatus.choices])
            end_time = None
            error_message = ""
            uvm_warnings = ""
            
            if status in [TaskStatus.SUCCESS, TaskStatus.FAILED]:
                duration_minutes = random.randint(10, 300)
                end_time = start_time + timedelta(minutes=duration_minutes)
                
                if status == TaskStatus.FAILED:
                    error_message = f"仿真错误: 测试用例 test_{i} 在时间 {random.randint(100, 1000)}ns 处失败"
                
                if random.random() > 0.7:  # 30% 概率有UVM警告
                    uvm_warnings = f"UVM_WARNING: 在组件 testbench.dut.module_{i} 中检测到未预期的信号变化"

            simulation_task = SimulationTask.objects.create(
                project=project,
                pc_node=pc_node,
                compile_task=compile_task,
                task_name=f'{project.name}_仿真任务_{i+1}',
                command=f'vsim -c -do "run -all" testbench_{i}',
                status=status,
                log_path=f'/logs/simulation/{project.name.lower()}/sim_{i+1}.log',
                start_time=start_time,
                end_time=end_time,
                uvm_warnings=uvm_warnings,
                error_message=error_message,
                created_at=created_time
            )
            simulation_tasks.append(simulation_task)

        self.stdout.write(f'创建了 {len(simulation_tasks)} 个仿真任务')

        # 创建验证任务
        verification_tasks = []
        for i in range(options['tasks']):
            project = random.choice(projects)
            pc_node = random.choice(pc_nodes)
            simulation_task = random.choice([st for st in simulation_tasks if st.project == project]) if random.random() > 0.4 else None
            
            created_time = timezone.now() - timedelta(days=random.randint(0, 20))
            start_time = created_time + timedelta(minutes=random.randint(1, 60))
            
            status = random.choice([s[0] for s in TaskStatus.choices])
            end_time = None
            error_message = ""
            notes = ""
            
            if status in [TaskStatus.SUCCESS, TaskStatus.FAILED]:
                duration_minutes = random.randint(5, 180)
                end_time = start_time + timedelta(minutes=duration_minutes)
                
                if status == TaskStatus.FAILED:
                    error_message = f"验证错误: 覆盖率检查失败，当前覆盖率 {random.randint(60, 89)}%，要求 90%"
                else:
                    notes = f"验证完成，覆盖率达到 {random.randint(90, 100)}%，所有测试用例通过"

            verification_task = VerificationTask.objects.create(
                project=project,
                pc_node=pc_node,
                simulation_task=simulation_task,
                task_name=f'{project.name}_验证任务_{i+1}',
                command=f'python verify.py --test-suite suite_{i} --coverage-target 90',
                status=status,
                log_path=f'/logs/verification/{project.name.lower()}/verify_{i+1}.log',
                document_path=f'/docs/verification/{project.name.lower()}/report_{i+1}.html',
                start_time=start_time,
                end_time=end_time,
                error_message=error_message,
                notes=notes,
                created_at=created_time
            )
            verification_tasks.append(verification_task)

        self.stdout.write(f'创建了 {len(verification_tasks)} 个验证任务')

        self.stdout.write(
            self.style.SUCCESS(
                f'示例数据创建完成!\n'
                f'- 项目: {len(projects)}\n'
                f'- PC节点: {len(pc_nodes)}\n'
                f'- 编译任务: {len(compile_tasks)}\n'
                f'- 仿真任务: {len(simulation_tasks)}\n'
                f'- 验证任务: {len(verification_tasks)}'
            )
        )
