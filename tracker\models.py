from django.db import models
from django.utils import timezone
from django.core.validators import FileExtensionValidator
import os


class Project(models.Model):
    """项目模型"""
    name = models.CharField(max_length=100, unique=True, verbose_name="项目名称")
    description = models.TextField(blank=True, verbose_name="项目描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "项目"
        verbose_name_plural = "项目"
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name


class PCNode(models.Model):
    """PC节点模型"""
    pc_id = models.CharField(max_length=50, unique=True, verbose_name="PC ID")
    hostname = models.CharField(max_length=100, verbose_name="主机名")
    ip_address = models.GenericIPAddressField(blank=True, null=True, verbose_name="IP地址")
    description = models.TextField(blank=True, verbose_name="描述")
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    
    class Meta:
        verbose_name = "PC节点"
        verbose_name_plural = "PC节点"
        ordering = ['pc_id']
    
    def __str__(self):
        return f"{self.pc_id} ({self.hostname})"


class TaskStatus(models.TextChoices):
    """任务状态枚举"""
    PENDING = 'pending', '等待中'
    RUNNING = 'running', '运行中'
    SUCCESS = 'success', '成功'
    FAILED = 'failed', '失败'
    CANCELLED = 'cancelled', '已取消'


class CompileTask(models.Model):
    """编译任务模型"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, verbose_name="项目")
    pc_node = models.ForeignKey(PCNode, on_delete=models.CASCADE, verbose_name="PC节点")
    task_name = models.CharField(max_length=200, verbose_name="任务名称")
    status = models.CharField(
        max_length=20, 
        choices=TaskStatus.choices, 
        default=TaskStatus.PENDING,
        verbose_name="状态"
    )
    command = models.TextField(verbose_name="执行命令")
    log_path = models.CharField(max_length=500, blank=True, verbose_name="日志路径")
    start_time = models.DateTimeField(null=True, blank=True, verbose_name="开始时间")
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="结束时间")
    duration = models.DurationField(null=True, blank=True, verbose_name="执行时长")
    error_message = models.TextField(blank=True, verbose_name="错误信息")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "编译任务"
        verbose_name_plural = "编译任务"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['project', 'status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.project.name} - {self.task_name}"
    
    @property
    def duration_display(self):
        """格式化显示执行时长"""
        if self.duration:
            total_seconds = int(self.duration.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            seconds = total_seconds % 60
            if hours > 0:
                return f"{hours}h {minutes}m {seconds}s"
            elif minutes > 0:
                return f"{minutes}m {seconds}s"
            else:
                return f"{seconds}s"
        return "-"
    
    def save(self, *args, **kwargs):
        # 自动计算执行时长
        if self.start_time and self.end_time:
            self.duration = self.end_time - self.start_time
        super().save(*args, **kwargs)


class SimulationTask(models.Model):
    """仿真任务模型"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, verbose_name="项目")
    pc_node = models.ForeignKey(PCNode, on_delete=models.CASCADE, verbose_name="PC节点")
    compile_task = models.ForeignKey(
        CompileTask, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        verbose_name="关联编译任务"
    )
    task_name = models.CharField(max_length=200, verbose_name="仿真名称")
    status = models.CharField(
        max_length=20, 
        choices=TaskStatus.choices, 
        default=TaskStatus.PENDING,
        verbose_name="状态"
    )
    command = models.TextField(verbose_name="执行命令")
    log_path = models.CharField(max_length=500, blank=True, verbose_name="日志路径")
    start_time = models.DateTimeField(null=True, blank=True, verbose_name="开始时间")
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="结束时间")
    duration = models.DurationField(null=True, blank=True, verbose_name="执行时长")
    uvm_warnings = models.TextField(blank=True, verbose_name="UVM警告")
    error_message = models.TextField(blank=True, verbose_name="错误信息")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "仿真任务"
        verbose_name_plural = "仿真任务"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['project', 'status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.project.name} - {self.task_name}"
    
    @property
    def duration_display(self):
        """格式化显示执行时长"""
        if self.duration:
            total_seconds = int(self.duration.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            seconds = total_seconds % 60
            if hours > 0:
                return f"{hours}h {minutes}m {seconds}s"
            elif minutes > 0:
                return f"{minutes}m {seconds}s"
            else:
                return f"{seconds}s"
        return "-"
    
    def save(self, *args, **kwargs):
        # 自动计算执行时长
        if self.start_time and self.end_time:
            self.duration = self.end_time - self.start_time
        super().save(*args, **kwargs)


class VerificationTask(models.Model):
    """验证任务模型"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, verbose_name="项目")
    pc_node = models.ForeignKey(PCNode, on_delete=models.CASCADE, verbose_name="PC节点")
    simulation_task = models.ForeignKey(
        SimulationTask, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        verbose_name="关联仿真任务"
    )
    task_name = models.CharField(max_length=200, verbose_name="验证任务名称")
    status = models.CharField(
        max_length=20, 
        choices=TaskStatus.choices, 
        default=TaskStatus.PENDING,
        verbose_name="状态"
    )
    command = models.TextField(verbose_name="执行命令")
    log_path = models.CharField(max_length=500, blank=True, verbose_name="日志路径")
    document_path = models.CharField(max_length=500, blank=True, verbose_name="文档路径")
    start_time = models.DateTimeField(null=True, blank=True, verbose_name="开始时间")
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="结束时间")
    duration = models.DurationField(null=True, blank=True, verbose_name="执行时长")
    error_message = models.TextField(blank=True, verbose_name="错误信息")
    notes = models.TextField(blank=True, verbose_name="备注")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "验证任务"
        verbose_name_plural = "验证任务"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['project', 'status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.project.name} - {self.task_name}"
    
    @property
    def duration_display(self):
        """格式化显示执行时长"""
        if self.duration:
            total_seconds = int(self.duration.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            seconds = total_seconds % 60
            if hours > 0:
                return f"{hours}h {minutes}m {seconds}s"
            elif minutes > 0:
                return f"{minutes}m {seconds}s"
            else:
                return f"{seconds}s"
        return "-"
    
    def save(self, *args, **kwargs):
        # 自动计算执行时长
        if self.start_time and self.end_time:
            self.duration = self.end_time - self.start_time
        super().save(*args, **kwargs)
