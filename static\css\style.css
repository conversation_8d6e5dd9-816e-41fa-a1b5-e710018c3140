/* DV跟踪工具自定义样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
}

.navbar-nav .nav-link.active {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 状态徽章样式 */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 按钮样式 */
.btn {
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-group .btn {
    border-radius: 0.375rem;
}

.btn-group .btn:not(:last-child) {
    margin-right: 0.25rem;
}

/* 分页样式 */
.pagination .page-link {
    color: #495057;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

.pagination .page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* 面包屑样式 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

/* 搜索结果高亮 */
.search-highlight {
    background-color: #fff3cd;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
}

/* 日志和文档查看样式 */
.log-content,
.document-content {
    background-color: #2d3748;
    color: #e2e8f0;
    border-radius: 0.375rem;
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

.log-content mark,
.document-content mark {
    background-color: #fbbf24;
    color: #1f2937;
    padding: 0.125rem;
    border-radius: 0.25rem;
}

/* 统计卡片样式 */
.stats-card {
    text-align: center;
    padding: 1.5rem;
}

.stats-card h4 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-card small {
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 任务状态颜色 */
.status-pending {
    color: #6c757d;
}

.status-running {
    color: #fd7e14;
}

.status-success {
    color: #198754;
}

.status-failed {
    color: #dc3545;
}

.status-cancelled {
    color: #6f42c1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }
    
    .card-body .row {
        margin-bottom: 1rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: #343a40;
    color: #fff;
    font-size: 0.875rem;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: #343a40;
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: #343a40;
}

/* 表单样式 */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 错误和警告样式 */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

/* 代码块样式 */
pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    font-size: 0.875rem;
    overflow-x: auto;
}

code {
    background-color: #f8f9fa;
    color: #e83e8c;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

/* 链接样式 */
a {
    text-decoration: none;
    transition: color 0.2s ease-in-out;
}

a:hover {
    text-decoration: underline;
}

/* 列表组样式 */
.list-group-item {
    border: 1px solid rgba(0, 0, 0, 0.125);
    transition: background-color 0.2s ease-in-out;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-item-action:hover {
    background-color: #e9ecef;
}

/* 页脚样式 */
footer {
    margin-top: auto;
    background-color: #343a40 !important;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .pagination,
    footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .table {
        border-collapse: collapse !important;
    }
    
    .table th,
    .table td {
        border: 1px solid #000 !important;
    }
}
