from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
from .models import Project, PCNode, CompileTask, SimulationTask, VerificationTask, TaskStatus


class ModelTestCase(TestCase):
    """模型测试用例"""
    
    def setUp(self):
        """设置测试数据"""
        self.project = Project.objects.create(
            name="测试项目",
            description="这是一个测试项目"
        )
        
        self.pc_node = PCNode.objects.create(
            pc_id="PC001",
            hostname="test-pc",
            ip_address="*************"
        )
    
    def test_project_creation(self):
        """测试项目创建"""
        self.assertEqual(self.project.name, "测试项目")
        self.assertEqual(str(self.project), "测试项目")
    
    def test_pc_node_creation(self):
        """测试PC节点创建"""
        self.assertEqual(self.pc_node.pc_id, "PC001")
        self.assertEqual(str(self.pc_node), "PC001 (test-pc)")
    
    def test_compile_task_creation(self):
        """测试编译任务创建"""
        task = CompileTask.objects.create(
            project=self.project,
            pc_node=self.pc_node,
            task_name="测试编译任务",
            command="make build",
            status=TaskStatus.SUCCESS,
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(minutes=5)
        )
        
        self.assertEqual(task.task_name, "测试编译任务")
        self.assertEqual(task.status, TaskStatus.SUCCESS)
        self.assertIsNotNone(task.duration)
        self.assertEqual(str(task), "测试项目 - 测试编译任务")
    
    def test_simulation_task_creation(self):
        """测试仿真任务创建"""
        compile_task = CompileTask.objects.create(
            project=self.project,
            pc_node=self.pc_node,
            task_name="编译任务",
            command="make build",
            status=TaskStatus.SUCCESS
        )
        
        sim_task = SimulationTask.objects.create(
            project=self.project,
            pc_node=self.pc_node,
            compile_task=compile_task,
            task_name="测试仿真任务",
            command="run_sim",
            status=TaskStatus.SUCCESS,
            uvm_warnings="Warning: Test warning"
        )
        
        self.assertEqual(sim_task.task_name, "测试仿真任务")
        self.assertEqual(sim_task.compile_task, compile_task)
        self.assertEqual(sim_task.uvm_warnings, "Warning: Test warning")
    
    def test_verification_task_creation(self):
        """测试验证任务创建"""
        sim_task = SimulationTask.objects.create(
            project=self.project,
            pc_node=self.pc_node,
            task_name="仿真任务",
            command="run_sim",
            status=TaskStatus.SUCCESS
        )
        
        ver_task = VerificationTask.objects.create(
            project=self.project,
            pc_node=self.pc_node,
            simulation_task=sim_task,
            task_name="测试验证任务",
            command="verify",
            status=TaskStatus.SUCCESS,
            notes="验证完成"
        )
        
        self.assertEqual(ver_task.task_name, "测试验证任务")
        self.assertEqual(ver_task.simulation_task, sim_task)
        self.assertEqual(ver_task.notes, "验证完成")


class ViewTestCase(TestCase):
    """视图测试用例"""
    
    def setUp(self):
        """设置测试数据"""
        self.client = Client()
        self.project = Project.objects.create(
            name="测试项目",
            description="这是一个测试项目"
        )
        
        self.pc_node = PCNode.objects.create(
            pc_id="PC001",
            hostname="test-pc",
            ip_address="*************"
        )
        
        self.compile_task = CompileTask.objects.create(
            project=self.project,
            pc_node=self.pc_node,
            task_name="测试编译任务",
            command="make build",
            status=TaskStatus.SUCCESS
        )
    
    def test_dashboard_view(self):
        """测试仪表板视图"""
        response = self.client.get(reverse('tracker:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "DV跟踪仪表板")
    
    def test_compile_tasks_view(self):
        """测试编译任务列表视图"""
        response = self.client.get(reverse('tracker:compile_tasks'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "编译任务")
        self.assertContains(response, self.compile_task.task_name)
    
    def test_task_detail_view(self):
        """测试任务详情视图"""
        response = self.client.get(
            reverse('tracker:task_detail', args=['compile', self.compile_task.id])
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.compile_task.task_name)
        self.assertContains(response, self.compile_task.command)
    
    def test_search_functionality(self):
        """测试搜索功能"""
        response = self.client.get(
            reverse('tracker:search_tasks'),
            {'q': '测试'}
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('results', data)
        self.assertTrue(len(data['results']) > 0)
    
    def test_task_filtering(self):
        """测试任务过滤"""
        response = self.client.get(
            reverse('tracker:compile_tasks'),
            {'status': TaskStatus.SUCCESS}
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.compile_task.task_name)
    
    def test_task_sorting(self):
        """测试任务排序"""
        # 创建另一个任务
        CompileTask.objects.create(
            project=self.project,
            pc_node=self.pc_node,
            task_name="另一个编译任务",
            command="make build2",
            status=TaskStatus.PENDING
        )
        
        response = self.client.get(
            reverse('tracker:compile_tasks'),
            {'sort': 'task_name'}
        )
        self.assertEqual(response.status_code, 200)


class APITestCase(TestCase):
    """API测试用例"""
    
    def setUp(self):
        """设置测试数据"""
        self.client = Client()
        self.project = Project.objects.create(
            name="API测试项目",
            description="API测试项目描述"
        )
        
        self.pc_node = PCNode.objects.create(
            pc_id="API001",
            hostname="api-test-pc"
        )
    
    def test_search_api_empty_query(self):
        """测试空搜索查询"""
        response = self.client.get(reverse('tracker:search_tasks'), {'q': ''})
        self.assertEqual(response.status_code, 400)
    
    def test_search_api_with_results(self):
        """测试有结果的搜索"""
        CompileTask.objects.create(
            project=self.project,
            pc_node=self.pc_node,
            task_name="API测试任务",
            command="make api_test",
            status=TaskStatus.SUCCESS
        )
        
        response = self.client.get(reverse('tracker:search_tasks'), {'q': 'API'})
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('results', data)
        self.assertTrue(len(data['results']) > 0)
        self.assertEqual(data['results'][0]['name'], 'API测试任务')
    
    def test_search_api_no_results(self):
        """测试无结果的搜索"""
        response = self.client.get(reverse('tracker:search_tasks'), {'q': '不存在的任务'})
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data['results']), 0)
