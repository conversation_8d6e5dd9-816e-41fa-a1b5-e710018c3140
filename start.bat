@echo off
echo ========================================
echo DV跟踪工具 - Windows启动脚本
echo ========================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查虚拟环境
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate

REM 安装依赖
echo 安装依赖包...
pip install -r requirements.txt

REM 检查.env文件
if not exist ".env" (
    if exist ".env.example" (
        echo 复制环境配置文件...
        copy .env.example .env
        echo 请编辑.env文件配置数据库连接信息
        pause
    )
)

REM 创建必要目录
if not exist "logs" mkdir logs
if not exist "media" mkdir media
if not exist "staticfiles" mkdir staticfiles

REM 数据库迁移
echo 执行数据库迁移...
python manage.py makemigrations
python manage.py migrate

REM 询问是否创建示例数据
set /p create_sample="是否创建示例数据? (y/n): "
if /i "%create_sample%"=="y" (
    echo 创建示例数据...
    python manage.py create_sample_data --projects 3 --pc-nodes 5 --tasks 20
)

REM 启动服务器
echo 启动开发服务器...
echo 服务器地址: http://localhost:8000
echo 按 Ctrl+C 停止服务器
python manage.py runserver

pause
