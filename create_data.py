#!/usr/bin/env python
import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dv_tracker.settings')
django.setup()

from tracker.models import Project, PCNode, CompileTask, SimulationTask, VerificationTask, TaskStatus

def create_sample_data():
    print("开始创建示例数据...")
    
    # 创建项目
    project1 = Project.objects.create(
        name="CPU设计项目",
        description="32位RISC-V CPU核心设计验证"
    )
    
    project2 = Project.objects.create(
        name="GPU渲染单元",
        description="图形处理单元渲染管线验证"
    )
    
    print(f"创建项目: {project1.name}, {project2.name}")
    
    # 创建PC节点
    pc1 = PCNode.objects.create(
        pc_id="PC001",
        hostname="dv-workstation-01",
        ip_address="*************"
    )
    
    pc2 = PCNode.objects.create(
        pc_id="PC002", 
        hostname="dv-workstation-02",
        ip_address="*************"
    )
    
    print(f"创建PC节点: {pc1.pc_id}, {pc2.pc_id}")
    
    # 创建编译任务
    compile1 = CompileTask.objects.create(
        project=project1,
        pc_node=pc1,
        task_name="CPU核心编译",
        command="make compile TARGET=cpu_core",
        status=TaskStatus.SUCCESS,
        log_path="/logs/compile/cpu_core_compile.log",
        start_time=datetime.now() - timedelta(hours=2),
        end_time=datetime.now() - timedelta(hours=1, minutes=45)
    )
    
    compile2 = CompileTask.objects.create(
        project=project2,
        pc_node=pc2,
        task_name="GPU渲染器编译",
        command="make compile TARGET=gpu_renderer",
        status=TaskStatus.FAILED,
        log_path="/logs/compile/gpu_renderer_compile.log",
        error_message="编译错误: 在文件 shader.v 第45行发现语法错误",
        start_time=datetime.now() - timedelta(hours=1),
        end_time=datetime.now() - timedelta(minutes=30)
    )
    
    print(f"创建编译任务: {compile1.task_name}, {compile2.task_name}")
    
    # 创建仿真任务
    sim1 = SimulationTask.objects.create(
        project=project1,
        pc_node=pc1,
        compile_task=compile1,
        task_name="CPU指令集仿真",
        command="vsim -c -do 'run -all' cpu_testbench",
        status=TaskStatus.SUCCESS,
        log_path="/logs/simulation/cpu_instruction_sim.log",
        start_time=datetime.now() - timedelta(hours=1, minutes=30),
        end_time=datetime.now() - timedelta(minutes=45),
        uvm_warnings="UVM_WARNING: 在组件 cpu_core.alu 中检测到未预期的信号变化"
    )
    
    sim2 = SimulationTask.objects.create(
        project=project2,
        pc_node=pc2,
        task_name="GPU像素管线仿真",
        command="vsim -c -do 'run -all' gpu_pipeline_tb",
        status=TaskStatus.RUNNING,
        log_path="/logs/simulation/gpu_pipeline_sim.log",
        start_time=datetime.now() - timedelta(minutes=20)
    )
    
    print(f"创建仿真任务: {sim1.task_name}, {sim2.task_name}")
    
    # 创建验证任务
    ver1 = VerificationTask.objects.create(
        project=project1,
        pc_node=pc1,
        simulation_task=sim1,
        task_name="CPU功能验证",
        command="python verify_cpu.py --coverage-target 95",
        status=TaskStatus.SUCCESS,
        log_path="/logs/verification/cpu_verification.log",
        document_path="/docs/verification/cpu_verification_report.html",
        start_time=datetime.now() - timedelta(minutes=40),
        end_time=datetime.now() - timedelta(minutes=10),
        notes="验证完成，覆盖率达到 97%，所有测试用例通过"
    )
    
    print(f"创建验证任务: {ver1.task_name}")
    
    print("示例数据创建完成！")
    print(f"- 项目: {Project.objects.count()}")
    print(f"- PC节点: {PCNode.objects.count()}")
    print(f"- 编译任务: {CompileTask.objects.count()}")
    print(f"- 仿真任务: {SimulationTask.objects.count()}")
    print(f"- 验证任务: {VerificationTask.objects.count()}")

if __name__ == '__main__':
    create_sample_data()
