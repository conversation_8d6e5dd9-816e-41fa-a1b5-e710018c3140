#!/bin/bash

echo "========================================"
echo "DV跟踪工具 - Linux/Mac启动脚本"
echo "========================================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "安装依赖包..."
pip install -r requirements.txt

# 检查.env文件
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        echo "复制环境配置文件..."
        cp .env.example .env
        echo "请编辑.env文件配置数据库连接信息"
        read -p "按回车键继续..."
    fi
fi

# 创建必要目录
mkdir -p logs media staticfiles

# 数据库迁移
echo "执行数据库迁移..."
python manage.py makemigrations
python manage.py migrate

# 询问是否创建示例数据
read -p "是否创建示例数据? (y/n): " create_sample
if [[ $create_sample == "y" || $create_sample == "Y" ]]; then
    echo "创建示例数据..."
    python manage.py create_sample_data --projects 3 --pc-nodes 5 --tasks 20
fi

# 启动服务器
echo "启动开发服务器..."
echo "服务器地址: http://localhost:8000"
echo "按 Ctrl+C 停止服务器"
python manage.py runserver
