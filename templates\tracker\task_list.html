{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if task_type == 'compile' %}编译任务
    {% elif task_type == 'simulation' %}仿真任务
    {% elif task_type == 'verification' %}验证任务
    {% endif %} - DV跟踪工具
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                {% if task_type == 'compile' %}
                    <i class="fas fa-hammer me-2"></i>编译任务
                {% elif task_type == 'simulation' %}
                    <i class="fas fa-play-circle me-2"></i>仿真任务
                {% elif task_type == 'verification' %}
                    <i class="fas fa-check-circle me-2"></i>验证任务
                {% endif %}
            </h1>
        </div>
    </div>
</div>

<!-- 过滤和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">搜索</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search_query }}" placeholder="任务名称、项目或PC ID">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">状态</label>
                <select class="form-select" id="status" name="status">
                    <option value="">全部状态</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="project" class="form-label">项目</label>
                <select class="form-select" id="project" name="project">
                    <option value="">全部项目</option>
                    {% for proj in projects %}
                        <option value="{{ proj.id }}" {% if project_filter == proj.id|stringformat:"s" %}selected{% endif %}>
                            {{ proj.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="sort" class="form-label">排序</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="-created_at" {% if sort_by == '-created_at' %}selected{% endif %}>创建时间(新到旧)</option>
                    <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>创建时间(旧到新)</option>
                    <option value="task_name" {% if sort_by == 'task_name' %}selected{% endif %}>任务名称(A-Z)</option>
                    <option value="-task_name" {% if sort_by == '-task_name' %}selected{% endif %}>任务名称(Z-A)</option>
                    <option value="status" {% if sort_by == 'status' %}selected{% endif %}>状态</option>
                    <option value="-duration" {% if sort_by == '-duration' %}selected{% endif %}>执行时长</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <a href="{% if task_type == 'compile' %}{% url 'tracker:compile_tasks' %}{% elif task_type == 'simulation' %}{% url 'tracker:simulation_tasks' %}{% elif task_type == 'verification' %}{% url 'tracker:verification_tasks' %}{% endif %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> 清除
                </a>
            </div>
        </form>
    </div>
</div>

<!-- 任务列表 -->
<div class="card">
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>任务名称</th>
                            <th>项目</th>
                            <th>PC节点</th>
                            <th>状态</th>
                            <th>执行时长</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in page_obj %}
                            <tr>
                                <td>
                                    <a href="{% url 'tracker:task_detail' task_type task.id %}" 
                                       class="text-decoration-none">
                                        {{ task.task_name }}
                                    </a>
                                </td>
                                <td>{{ task.project.name }}</td>
                                <td>{{ task.pc_node.pc_id }}</td>
                                <td>
                                    <span class="badge bg-{% if task.status == 'success' %}success{% elif task.status == 'failed' %}danger{% elif task.status == 'running' %}warning{% elif task.status == 'pending' %}secondary{% else %}info{% endif %}">
                                        {{ task.get_status_display }}
                                    </span>
                                </td>
                                <td>{{ task.duration_display }}</td>
                                <td>{{ task.created_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'tracker:task_detail' task_type task.id %}" 
                                           class="btn btn-outline-primary" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if task.log_path %}
                                            <a href="{% url 'tracker:view_log' task_type task.id %}" 
                                               class="btn btn-outline-info" title="查看日志">
                                                <i class="fas fa-file-alt"></i>
                                            </a>
                                        {% endif %}
                                        {% if task_type == 'verification' and task.document_path %}
                                            <a href="{% url 'tracker:view_document' task.id %}"
                                               class="btn btn-outline-success" title="查看文档">
                                                <i class="fas fa-file-alt"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="任务列表分页">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if status_filter %}status={{ status_filter }}&{% endif %}{% if project_filter %}project={{ project_filter }}&{% endif %}{% if sort_by %}sort={{ sort_by }}&{% endif %}page={{ page_obj.previous_page_number }}">
                                    上一页
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if status_filter %}status={{ status_filter }}&{% endif %}{% if project_filter %}project={{ project_filter }}&{% endif %}{% if sort_by %}sort={{ sort_by }}&{% endif %}page={{ num }}">
                                        {{ num }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if status_filter %}status={{ status_filter }}&{% endif %}{% if project_filter %}project={{ project_filter }}&{% endif %}{% if sort_by %}sort={{ sort_by }}&{% endif %}page={{ page_obj.next_page_number }}">
                                    下一页
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>

                <div class="text-center text-muted">
                    显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 条，共 {{ page_obj.paginator.count }} 条记录
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无任务数据</h5>
                <p class="text-muted">当前没有找到符合条件的任务。</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
